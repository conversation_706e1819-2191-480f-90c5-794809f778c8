/**
 * Project Dashboard Component
 * Comprehensive project management for buyer persona generator
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Heart, 
  Users, 
  MessageSquare, 
  Calendar,
  MoreVertical,
  Edit,
  Archive,
  Trash2,
  Star,
  Eye,
  Clock
} from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { usePersonaDatabase } from '../hooks/use-persona-database'
import { ProjectDialog } from './project-dialog'
import type { BuyerPersonaProject } from '@/services/buyerPersonaService'

interface ProjectDashboardProps {
  onCreateProject: () => void
  onOpenProject: (project: BuyerPersonaProject) => void
  onEditProject: (project: BuyerPersonaProject) => void
}

export function ProjectDashboard({ onCreateProject, onOpenProject, onEditProject }: ProjectDashboardProps) {
  const {
    projects,
    projectsLoading,
    projectsError,
    userStats,
    loadProjects,
    updateProject,
    deleteProject,
    toggleProjectFavorite,
    loadUserStats
  } = usePersonaDatabase()

  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'archived' | 'favorites'>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'recent' | 'name' | 'personas' | 'conversations'>('recent')

  useEffect(() => {
    loadProjects()
    loadUserStats()
  }, [loadProjects, loadUserStats])

  // Filter and sort projects
  const filteredProjects = projects
    .filter(project => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        if (!project.project_name.toLowerCase().includes(query) &&
            !project.description?.toLowerCase().includes(query) &&
            !project.product_description.toLowerCase().includes(query)) {
          return false
        }
      }

      // Status filter
      switch (filterStatus) {
        case 'active':
          return project.project_status === 'active'
        case 'archived':
          return project.project_status === 'archived'
        case 'favorites':
          return project.is_favorite
        default:
          return true
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.project_name.localeCompare(b.project_name)
        case 'personas':
          return b.personas_count - a.personas_count
        case 'conversations':
          return b.conversations_count - a.conversations_count
        case 'recent':
        default:
          return new Date(b.last_accessed_at || b.updated_at).getTime() - 
                 new Date(a.last_accessed_at || a.updated_at).getTime()
      }
    })

  const handleToggleFavorite = async (project: BuyerPersonaProject, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      await toggleProjectFavorite(project.id, !project.is_favorite)
    } catch (error) {
      console.error('Error toggling favorite:', error)
    }
  }

  const handleArchiveProject = async (project: BuyerPersonaProject, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      const newStatus = project.project_status === 'archived' ? 'active' : 'archived'
      await updateProject(project.id, { project_status: newStatus })
    } catch (error) {
      console.error('Error archiving project:', error)
    }
  }

  const handleDeleteProject = async (project: BuyerPersonaProject, e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm(`Are you sure you want to delete "${project.project_name}"? This action cannot be undone.`)) {
      try {
        await deleteProject(project.id)
      } catch (error) {
        console.error('Error deleting project:', error)
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'archived':
        return 'bg-gray-100 text-gray-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Buyer Persona Projects</h1>
          <p className="text-gray-600">Manage and organize your buyer persona generations</p>
        </div>
        <Button onClick={onCreateProject} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          New Project
        </Button>
      </div>

      {/* Stats Cards */}
      {userStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Users className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{userStats.totalProjects}</p>
                  <p className="text-sm text-gray-600">Total Projects</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Users className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{userStats.totalPersonas}</p>
                  <p className="text-sm text-gray-600">Total Personas</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <MessageSquare className="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{userStats.totalConversations}</p>
                  <p className="text-sm text-gray-600">Conversations</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <Heart className="w-5 h-5 text-red-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-gray-900">{userStats.favoriteProjects}</p>
                  <p className="text-sm text-gray-600">Favorites</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-3">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Tabs value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="favorites">Favorites</TabsTrigger>
              <TabsTrigger value="archived">Archived</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <div className="flex gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Projects Grid/List */}
      {projectsLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading projects...</p>
          </div>
        </div>
      ) : filteredProjects.length > 0 ? (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          <AnimatePresence>
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                layout
              >
                {viewMode === 'grid' ? (
                  <Card 
                    className="hover:shadow-lg transition-shadow cursor-pointer group"
                    onClick={() => onOpenProject(project)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg line-clamp-1">{project.project_name}</CardTitle>
                          <CardDescription className="line-clamp-2 mt-1">
                            {project.description || project.product_description}
                          </CardDescription>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEditProject(project) }}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => handleToggleFavorite(project, e)}>
                              <Heart className={`w-4 h-4 mr-2 ${project.is_favorite ? 'fill-current text-red-500' : ''}`} />
                              {project.is_favorite ? 'Remove from Favorites' : 'Add to Favorites'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={(e) => handleArchiveProject(project, e)}>
                              <Archive className="w-4 h-4 mr-2" />
                              {project.project_status === 'archived' ? 'Unarchive' : 'Archive'}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={(e) => handleDeleteProject(project, e)}
                              className="text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(project.project_status)}>
                          {project.project_status}
                        </Badge>
                        {project.is_favorite && (
                          <Badge variant="outline" className="text-red-600 border-red-200">
                            <Heart className="w-3 h-3 mr-1 fill-current" />
                            Favorite
                          </Badge>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4 text-gray-400" />
                          <span>{project.personas_count} personas</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MessageSquare className="w-4 h-4 text-gray-400" />
                          <span>{project.conversations_count} conversations</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Eye className="w-4 h-4 text-gray-400" />
                          <span>{project.view_count} views</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span>{formatDate(project.last_accessed_at || project.updated_at)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  /* List View */
                  <Card 
                    className="hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => onOpenProject(project)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 flex-1">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">{project.project_name}</h3>
                            <p className="text-gray-600 text-sm line-clamp-1">
                              {project.description || project.product_description}
                            </p>
                          </div>
                          <div className="flex items-center gap-6 text-sm text-gray-600">
                            <span>{project.personas_count} personas</span>
                            <span>{project.conversations_count} conversations</span>
                            <span>{formatDate(project.last_accessed_at || project.updated_at)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(project.project_status)}>
                              {project.project_status}
                            </Badge>
                            {project.is_favorite && (
                              <Heart className="w-4 h-4 text-red-500 fill-current" />
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      ) : (
        <div className="text-center py-12">
          <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchQuery || filterStatus !== 'all' ? 'No projects found' : 'No projects yet'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || filterStatus !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Create your first buyer persona project to get started'
            }
          </p>
          {(!searchQuery && filterStatus === 'all') && (
            <Button onClick={onCreateProject}>
              Create Your First Project
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
