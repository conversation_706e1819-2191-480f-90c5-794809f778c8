/**
 * Migration Dialog Component
 * Handles the migration from localStorage to database for buyer persona data
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Database, 
  Download, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  X, 
  RefreshCw,
  Archive,
  Users,
  MessageSquare
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { PersonaMigrationManager } from '../utils/migration-manager'

interface MigrationDialogProps {
  isOpen: boolean
  onClose: () => void
  onMigrationComplete?: () => void
}

interface MigrationStatus {
  isCompleted: boolean
  hasLocalData: boolean
  localDataCount: number
  databaseDataCount: number
}

export function MigrationDialog({ isOpen, onClose, onMigrationComplete }: MigrationDialogProps) {
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [migrationResult, setMigrationResult] = useState<any>(null)
  const [currentStep, setCurrentStep] = useState<'check' | 'migrate' | 'complete'>('check')
  const [progress, setProgress] = useState(0)

  // Load migration status on mount
  useEffect(() => {
    if (isOpen) {
      loadMigrationStatus()
    }
  }, [isOpen])

  const loadMigrationStatus = async () => {
    try {
      setIsLoading(true)
      const status = await PersonaMigrationManager.getMigrationStatus()
      setMigrationStatus(status)
      
      if (status.isCompleted) {
        setCurrentStep('complete')
      } else if (status.hasLocalData) {
        setCurrentStep('check')
      } else {
        setCurrentStep('complete')
      }
    } catch (error) {
      console.error('Failed to load migration status:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleMigration = async () => {
    try {
      setIsLoading(true)
      setCurrentStep('migrate')
      setProgress(0)

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      const result = await PersonaMigrationManager.performMigration()
      
      clearInterval(progressInterval)
      setProgress(100)
      setMigrationResult(result)
      setCurrentStep('complete')

      if (result.success) {
        // Clear localStorage data after successful migration
        PersonaMigrationManager.clearLocalStorageData()
        onMigrationComplete?.()
      }

      // Reload status
      await loadMigrationStatus()
    } catch (error) {
      console.error('Migration failed:', error)
      setMigrationResult({
        success: false,
        errors: [`Migration failed: ${error}`],
        projectsCreated: 0,
        personasCreated: 0,
        conversationsCreated: 0,
        warnings: []
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    setMigrationResult(null)
    setCurrentStep('check')
    setProgress(0)
    onClose()
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-2xl"
        >
          <Card className="bg-white shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Database className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Data Migration</CardTitle>
                    <CardDescription>
                      Migrate your buyer persona data to the database
                    </CardDescription>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Migration Status Check */}
              {currentStep === 'check' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
                      <span className="ml-2">Checking data...</span>
                    </div>
                  ) : migrationStatus ? (
                    <>
                      {migrationStatus.isCompleted ? (
                        <Alert>
                          <CheckCircle className="w-4 h-4" />
                          <AlertDescription>
                            Migration has already been completed. Your data is safely stored in the database.
                          </AlertDescription>
                        </Alert>
                      ) : migrationStatus.hasLocalData ? (
                        <>
                          <Alert>
                            <AlertCircle className="w-4 h-4" />
                            <AlertDescription>
                              We found buyer persona data in your browser storage that can be migrated to the database for better persistence and organization.
                            </AlertDescription>
                          </Alert>

                          <div className="grid grid-cols-3 gap-4">
                            <div className="text-center p-4 bg-gray-50 rounded-lg">
                              <Archive className="w-8 h-8 mx-auto mb-2 text-gray-600" />
                              <div className="text-2xl font-bold text-gray-900">{migrationStatus.localDataCount}</div>
                              <div className="text-sm text-gray-600">Local Items</div>
                            </div>
                            <div className="text-center p-4 bg-blue-50 rounded-lg">
                              <Database className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                              <div className="text-2xl font-bold text-blue-900">{migrationStatus.databaseDataCount}</div>
                              <div className="text-sm text-blue-600">Database Projects</div>
                            </div>
                            <div className="text-center p-4 bg-green-50 rounded-lg">
                              <Users className="w-8 h-8 mx-auto mb-2 text-green-600" />
                              <div className="text-2xl font-bold text-green-900">∞</div>
                              <div className="text-sm text-green-600">Future Personas</div>
                            </div>
                          </div>

                          <div className="flex gap-3">
                            <Button onClick={handleMigration} className="flex-1">
                              <Upload className="w-4 h-4 mr-2" />
                              Start Migration
                            </Button>
                            <Button variant="outline" onClick={handleClose}>
                              Skip for Now
                            </Button>
                          </div>
                        </>
                      ) : (
                        <Alert>
                          <CheckCircle className="w-4 h-4" />
                          <AlertDescription>
                            No local data found to migrate. You're all set to start creating buyer personas!
                          </AlertDescription>
                        </Alert>
                      )}
                    </>
                  )}
                </motion.div>
              )}

              {/* Migration Progress */}
              {currentStep === 'migrate' && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  <div className="text-center">
                    <RefreshCw className="w-12 h-12 mx-auto mb-4 animate-spin text-blue-600" />
                    <h3 className="text-lg font-semibold mb-2">Migrating Your Data</h3>
                    <p className="text-gray-600 mb-4">
                      Please wait while we transfer your buyer persona data to the database...
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Migration Progress</span>
                      <span>{progress}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>

                  <Alert>
                    <AlertCircle className="w-4 h-4" />
                    <AlertDescription>
                      Please don't close this window during migration. Your data is being safely transferred.
                    </AlertDescription>
                  </Alert>
                </motion.div>
              )}

              {/* Migration Complete */}
              {currentStep === 'complete' && migrationResult && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-4"
                >
                  {migrationResult.success ? (
                    <>
                      <div className="text-center">
                        <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-600" />
                        <h3 className="text-lg font-semibold mb-2">Migration Completed!</h3>
                        <p className="text-gray-600">
                          Your buyer persona data has been successfully migrated to the database.
                        </p>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-green-50 rounded-lg">
                          <Archive className="w-8 h-8 mx-auto mb-2 text-green-600" />
                          <div className="text-2xl font-bold text-green-900">{migrationResult.projectsCreated}</div>
                          <div className="text-sm text-green-600">Projects Created</div>
                        </div>
                        <div className="text-center p-4 bg-blue-50 rounded-lg">
                          <Users className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                          <div className="text-2xl font-bold text-blue-900">{migrationResult.personasCreated}</div>
                          <div className="text-sm text-blue-600">Personas Migrated</div>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-lg">
                          <MessageSquare className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                          <div className="text-2xl font-bold text-purple-900">{migrationResult.conversationsCreated}</div>
                          <div className="text-sm text-purple-600">Conversations</div>
                        </div>
                      </div>

                      {migrationResult.warnings.length > 0 && (
                        <Alert>
                          <AlertCircle className="w-4 h-4" />
                          <AlertDescription>
                            <div className="font-medium mb-1">Warnings:</div>
                            <ul className="text-sm space-y-1">
                              {migrationResult.warnings.map((warning: string, index: number) => (
                                <li key={index}>• {warning}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                    </>
                  ) : (
                    <>
                      <div className="text-center">
                        <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-600" />
                        <h3 className="text-lg font-semibold mb-2">Migration Failed</h3>
                        <p className="text-gray-600">
                          There was an error during migration. Your original data is safe.
                        </p>
                      </div>

                      {migrationResult.errors.length > 0 && (
                        <Alert variant="destructive">
                          <AlertCircle className="w-4 h-4" />
                          <AlertDescription>
                            <div className="font-medium mb-1">Errors:</div>
                            <ul className="text-sm space-y-1">
                              {migrationResult.errors.map((error: string, index: number) => (
                                <li key={index}>• {error}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                    </>
                  )}

                  <div className="flex gap-3">
                    <Button onClick={handleClose} className="flex-1">
                      {migrationResult.success ? 'Continue' : 'Close'}
                    </Button>
                    {!migrationResult.success && (
                      <Button variant="outline" onClick={handleMigration}>
                        Retry Migration
                      </Button>
                    )}
                  </div>
                </motion.div>
              )}

              {/* No Migration Result but Complete Step */}
              {currentStep === 'complete' && !migrationResult && migrationStatus?.isCompleted && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center space-y-4"
                >
                  <CheckCircle className="w-12 h-12 mx-auto text-green-600" />
                  <h3 className="text-lg font-semibold">All Set!</h3>
                  <p className="text-gray-600">
                    Your buyer persona data is already in the database and ready to use.
                  </p>
                  <Button onClick={handleClose} className="w-full">
                    Continue
                  </Button>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
