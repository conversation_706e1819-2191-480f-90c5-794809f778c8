/**
 * Project Dialog Component
 * Create and edit buyer persona projects
 */

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { 
  X, 
  Save, 
  Loader2, 
  Users, 
  Target, 
  Building, 
  Globe,
  Tag,
  FileText
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form'

import { usePersonaDatabase } from '../hooks/use-persona-database'
import type { BuyerPersonaProject, CreateBuyerPersonaProjectData } from '@/services/buyerPersonaService'

// Form validation schema
const projectSchema = z.object({
  project_name: z.string().min(1, 'El nombre del proyecto es requerido').max(100, 'El nombre del proyecto es muy largo'),
  description: z.string().optional(),
  product_description: z.string().min(10, 'La descripción del producto debe tener al menos 10 caracteres'),
  industry: z.string().optional(),
  target_market: z.string().optional(),
  business_goals: z.string().optional(),
  competitors: z.string().optional(),
  target_countries: z.array(z.string()).default([]),
  num_personas: z.number().min(1).max(10).default(3),
  tags: z.array(z.string()).default([])
})

type ProjectFormData = z.infer<typeof projectSchema>

interface ProjectDialogProps {
  isOpen: boolean
  onClose: () => void
  project?: BuyerPersonaProject | null
  onSuccess?: (project: BuyerPersonaProject) => void
}

const INDUSTRY_OPTIONS = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
  'Real Estate',
  'Consulting',
  'Marketing',
  'E-commerce',
  'SaaS',
  'Other'
]

const COUNTRY_OPTIONS = [
  'United States',
  'Canada',
  'United Kingdom',
  'Germany',
  'France',
  'Spain',
  'Italy',
  'Australia',
  'Japan',
  'Brazil',
  'Mexico',
  'Other'
]

export function ProjectDialog({ isOpen, onClose, project, onSuccess }: ProjectDialogProps) {
  const { createProject, updateProject } = usePersonaDatabase()
  const [isLoading, setIsLoading] = useState(false)
  const [newTag, setNewTag] = useState('')

  const isEditing = !!project

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      project_name: '',
      description: '',
      product_description: '',
      industry: '',
      target_market: '',
      business_goals: '',
      competitors: '',
      target_countries: [],
      num_personas: 3,
      tags: []
    }
  })

  // Load project data when editing
  useEffect(() => {
    if (project && isOpen) {
      form.reset({
        project_name: project.project_name,
        description: project.description || '',
        product_description: project.product_description,
        industry: project.industry || '',
        target_market: project.target_market || '',
        business_goals: project.business_goals || '',
        competitors: project.competitors || '',
        target_countries: project.target_countries || [],
        num_personas: project.num_personas,
        tags: project.tags || []
      })
    } else if (!project && isOpen) {
      form.reset({
        project_name: '',
        description: '',
        product_description: '',
        industry: '',
        target_market: '',
        business_goals: '',
        competitors: '',
        target_countries: [],
        num_personas: 3,
        tags: []
      })
    }
  }, [project, isOpen, form])

  const onSubmit = async (data: ProjectFormData) => {
    setIsLoading(true)
    try {
      let result: BuyerPersonaProject

      if (isEditing && project) {
        result = await updateProject(project.id, data)
      } else {
        result = await createProject(data)
      }

      onSuccess?.(result)
      onClose()
    } catch (error) {
      console.error('Error saving project:', error)
      // Handle error (could show toast notification)
    } finally {
      setIsLoading(false)
    }
  }

  const addTag = () => {
    if (newTag.trim() && !form.getValues('tags').includes(newTag.trim())) {
      const currentTags = form.getValues('tags')
      form.setValue('tags', [...currentTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags')
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
          className="w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          <Card className="bg-white shadow-2xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-xl">
                    {isEditing ? 'Editar Proyecto' : 'Crear Nuevo Proyecto'}
                  </CardTitle>
                  <CardDescription>
                    {isEditing
                      ? 'Actualiza los detalles de tu proyecto de buyer persona'
                      : 'Configura un nuevo proyecto de buyer persona para organizar tu trabajo'
                    }
                  </CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  {/* Basic Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Basic Information
                    </h3>

                    <FormField
                      control={form.control}
                      name="project_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., SaaS Platform Buyer Personas" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Brief description of this project..."
                              className="min-h-[80px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Optional description to help you remember the project's purpose
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="product_description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Product/Service Description *</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Describe your product or service in detail..."
                              className="min-h-[100px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            This will be used to generate relevant buyer personas
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Business Context */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Building className="w-5 h-5" />
                      Business Context
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="industry"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Industry</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select industry" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {INDUSTRY_OPTIONS.map((industry) => (
                                  <SelectItem key={industry} value={industry}>
                                    {industry}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="num_personas"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Number of Personas</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                min="1" 
                                max="10" 
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 3)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="target_market"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Target Market</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Small to medium businesses, Enterprise customers" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="business_goals"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Business Goals</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="What are you trying to achieve with these personas?"
                              className="min-h-[80px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="competitors"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Key Competitors</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Competitor A, Competitor B" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Tags */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Tag className="w-5 h-5" />
                      Tags
                    </h3>

                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add a tag..."
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyPress={handleKeyPress}
                          className="flex-1"
                        />
                        <Button type="button" onClick={addTag} variant="outline">
                          Add
                        </Button>
                      </div>

                      {form.watch('tags').length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {form.watch('tags').map((tag) => (
                            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                              {tag}
                              <button
                                type="button"
                                onClick={() => removeTag(tag)}
                                className="ml-1 hover:text-red-600"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-3 pt-4">
                    <Button type="button" variant="outline" onClick={onClose} className="flex-1">
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isLoading} className="flex-1">
                      {isLoading ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          {isEditing ? 'Actualizando...' : 'Creando...'}
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          {isEditing ? 'Actualizar Proyecto' : 'Crear Proyecto'}
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
