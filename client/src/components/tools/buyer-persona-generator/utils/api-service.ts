/**
 * API service utilities for Buyer Persona Generator
 */

import { FormData, GenerationResult, PremiumFeatureType, ConversationData, ApiError } from '../types';

export class PersonaApiService {
  private static readonly BASE_URL = '/api';
  private static readonly PREMIUM_BASE_URL = '/api/v1/premium';

  /**
   * Check API health and service availability
   */
  static async checkHealth(): Promise<{ status: string; details?: any }> {
    try {
      const response = await fetch(`${this.BASE_URL}/buyer-persona/health`);

      if (response.ok) {
        const data = await response.json();
        return { status: 'healthy', details: data };
      } else {
        return { status: 'unhealthy', details: { statusCode: response.status, statusText: response.statusText } };
      }
    } catch (error) {
      return { status: 'error', details: { error: error instanceof Error ? error.message : 'Unknown error' } };
    }
  }

  /**
   * Generate buyer personas
   */
  static async generatePersonas(formData: FormData): Promise<GenerationResult> {
    const uniqueTimestamp = Date.now();
    const randomSeed = Math.random().toString(36).substr(2, 9);
    
    const requestBody = {
      product_description: formData.product_description,
      num_personas: formData.num_personas || 3,
      industry: formData.industry || "General",
      target_market: formData.target_market || "Profesionales y empresas",
      business_goals: formData.business_goals || "Aumentar ventas y mejorar engagement con clientes",
      competitors: formData.competitors || "Competidores del sector",
      target_countries: formData.target_countries || [],
      request_timestamp: uniqueTimestamp,
      request_id: `req_${uniqueTimestamp}_${randomSeed}`,
      unique_seed: `${uniqueTimestamp}_${randomSeed}_${formData.product_description.length}`,
      generation_context: `Generated at ${new Date().toISOString()} for product: ${formData.product_description.substring(0, 50)}...`
    };

    console.log("🚀 Sending buyer persona request:", requestBody);

    const response = await fetch(`${this.BASE_URL}/generate-buyer-personas`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Request-ID": requestBody.request_id,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      let errorMessage = `Error ${response.status}: ${response.statusText}`;

      // Read the response body only ONCE
      try {
        const responseText = await response.text();
        console.error('Raw error response:', responseText);

        if (responseText) {
          try {
            // Try to parse as JSON first
            const errorData = JSON.parse(responseText);

            // Check if it's a BuyerPersonaResponse with error status
            if (errorData.status === 'error' && errorData.error_message) {
              errorMessage = errorData.error_message;
            } else if (errorData.detail) {
              errorMessage = errorData.detail;
            } else if (errorData.error) {
              errorMessage = errorData.error;
            }

            console.error('Parsed API Error Details:', errorData);
          } catch (jsonError) {
            // If not JSON, use the text as-is
            console.error('Response is not JSON, using as text:', responseText);
            errorMessage = `${errorMessage} - ${responseText}`;
          }
        } else {
          console.error('Empty response body');
          errorMessage = `${errorMessage} - Empty response from server`;
        }
      } catch (readError) {
        console.error('Failed to read error response:', readError);
        errorMessage = `${errorMessage} - Could not read server response`;
      }

      throw new Error(errorMessage);
    }

    const data = await response.json();
    
    // Enrich data with additional context
    return {
      ...data,
      original_product_description: formData.product_description,
      generation_timestamp: Date.now()
    };
  }

  /**
   * Load premium feature
   */
  static async loadPremiumFeature(
    featureType: PremiumFeatureType,
    personaData: any,
    productDescription?: string
  ): Promise<any> {
    let requestBody: any;
    let endpoint: string;

    switch (featureType) {
      case 'avatars':
        endpoint = `${this.PREMIUM_BASE_URL}/avatars/generate`;
        requestBody = {
          persona_description: `${personaData.name}, ${personaData.age} años, ${personaData.job.title} en ${personaData.job.industry}. ${personaData.personal_background}`,
          style: "professional",
          gender: personaData.gender === "Masculino" ? "male" : personaData.gender === "Femenino" ? "female" : "neutral",
          age: personaData.age,
          ethnicity: "diverse"
        };
        break;

      case 'behavior':
        endpoint = `${this.PREMIUM_BASE_URL}/behavior/predict`;
        requestBody = {
          persona_data: {
            name: personaData.name,
            age: personaData.age,
            job: {
              title: personaData.job.title,
              industry: personaData.job.industry,
              company_size: personaData.job.company_size
            },
            goals: personaData.goals || ["Crecimiento profesional"],
            challenges: personaData.challenges || ["Gestión del tiempo"]
          },
          product_info: {
            description: productDescription || "Producto o servicio",
            category: "software"
          }
        };
        break;

      case 'conversation':
        endpoint = `${this.PREMIUM_BASE_URL}/conversation/start`;
        requestBody = {
          persona_data: {
            name: personaData.name,
            age: personaData.age,
            job_title: personaData.job.title,
            personality_traits: personaData.influences || ["Profesional"],
            communication_style: "professional",
            background: personaData.personal_background || "Profesional con experiencia"
          },
          conversation_type: "sales_discovery",
          context: productDescription || "Producto o servicio",
          product_info: {
            description: productDescription || "Producto o servicio",
            category: "software"
          }
        };
        break;

      case 'geographic':
        endpoint = `${this.PREMIUM_BASE_URL}/geographic/analyze`;
        requestBody = {
          persona_data: {
            name: personaData.name,
            age: personaData.age,
            location: personaData.location,
            job: {
              title: personaData.job.title,
              industry: personaData.job.industry,
              company_size: personaData.job.company_size
            },
            education: personaData.education,
            income_level: personaData.income_level
          },
          target_regions: [personaData.location.split(',')[1]?.trim() || "España"]
        };
        break;

      default:
        throw new Error(`Unsupported feature type: ${featureType}`);
    }

    console.log(`📤 Making API call to: ${endpoint}`);
    console.log(`📤 Request body:`, requestBody);

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    console.log(`📥 Response status: ${response.status}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`❌ API Error for ${featureType}:`, errorData);
      throw new Error(errorData.detail || `Error ${response.status} al cargar ${featureType}`);
    }

    const data = await response.json();
    console.log(`✅ Premium ${featureType} response:`, data);

    if (data.status !== "success") {
      throw new Error(data.error_message || "Error al cargar funcionalidad premium");
    }

    return data;
  }

  /**
   * Start conversation with persona
   */
  static async startConversation(conversationData: ConversationData): Promise<any> {
    const productDescription = conversationData.product_context || "Producto o servicio no especificado";

    const requestBody = {
      persona_data: {
        ...conversationData,
        job: conversationData.job || {},
        goals: conversationData.goals || [],
        challenges: conversationData.challenges || [],
        objections: conversationData.objections || [],
        communication_channels: conversationData.communication_channels || [],
        influences: conversationData.influences || []
      },
      conversation_type: "sales",
      context: `El usuario es un vendedor que quiere presentar su producto/servicio: "${productDescription}". La persona debe actuar como un cliente potencial interesado pero con dudas y objeciones naturales basadas en su perfil específico.`,
      product_info: {
        description: productDescription,
        category: "software"
      }
    };

    console.log("🚀 Starting conversation with request:", requestBody);

    const response = await fetch(`${this.PREMIUM_BASE_URL}/conversation/start`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("✅ Conversation started:", data);

    if (data.status !== "success") {
      throw new Error(data.error_message || "Error al iniciar conversación");
    }

    return data;
  }

  /**
   * Continue conversation
   */
  static async continueConversation(
    conversationId: string,
    userMessage: string,
    conversationData: ConversationData,
    messages: any[]
  ): Promise<any> {
    const productDescription = conversationData.product_context || "Producto o servicio no especificado";

    const requestBody = {
      conversation_id: conversationId,
      user_message: userMessage,
      conversation_data: {
        context: {
          persona_data: conversationData,
          product_info: {
            description: productDescription,
            category: "software"
          }
        },
        messages: messages
      }
    };

    console.log("📤 Continue conversation request:", requestBody);

    const response = await fetch(`${this.PREMIUM_BASE_URL}/conversation/continue`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Error ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("✅ Conversation continued:", data);

    if (data.status !== "success") {
      throw new Error(data.error_message || "Error al continuar conversación");
    }

    return data;
  }

  /**
   * Handle API errors consistently
   */
  static handleApiError(error: any): string {
    if (error.message) {
      return error.message;
    }
    
    if (typeof error === 'string') {
      return error;
    }
    
    return "Error desconocido. Por favor, inténtalo de nuevo.";
  }
}
