/**
 * Refactored Buyer Persona Generator - Main Component
 *
 * This is the new, modular version of the buyer persona generator
 * following best practices with proper separation of concerns.
 */

import React from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Database, RefreshCw, Users, Heart } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Components
import { PremiumBackground } from "../buyer-persona/premium-background";
import { PremiumHeader } from "../buyer-persona/premium-header";
import { PersonaFormContainer } from "./components/persona-form-container";
import { GenerationLoading } from "../buyer-persona/generation-loading";
import { ResultsContainer } from "./components/results-container";
import { PersonaErrorBoundary } from "./components/error-boundary";
import { HistoryDashboard } from "./components/history-dashboard";

// Hooks and utilities
import { usePersonaGeneratorEnhanced } from "./hooks/use-persona-generator-enhanced";
import { MigrationDialog } from "./components/migration-dialog";
import { ProjectDashboard } from "./components/project-dashboard";
import { ProjectDialog } from "./components/project-dialog";
import { Toaster } from "@/components/ui/toaster";

export default function BuyerPersonaGeneratorRefactored() {
  const {
    // Database state
    database,

    // Generation state
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,

    // Migration state
    showMigrationDialog,
    migrationStatus,

    // Form
    form,

    // Refs
    resultsRef,

    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    backToProjects,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    setViewMode,

    // Migration actions
    checkMigrationStatus,
    showMigration,
    hideMigration,
    onMigrationComplete,

    // Project actions
    createProjectFromGeneration,
    loadProjectResults
  } = usePersonaGeneratorEnhanced();

  // Project dialog state
  const [showProjectDialog, setShowProjectDialog] = React.useState(false);
  const [editingProject, setEditingProject] = React.useState<any>(null);

  // Project dialog handlers
  const handleCreateProject = () => {
    setEditingProject(null);
    setShowProjectDialog(true);
  };

  const handleEditProject = (project: any) => {
    setEditingProject(project);
    setShowProjectDialog(true);
  };

  const handleProjectDialogClose = () => {
    setShowProjectDialog(false);
    setEditingProject(null);
  };

  const handleProjectSuccess = async (project: any) => {
    console.log('🎉 Project success handler called with:', project);
    setShowProjectDialog(false);
    setEditingProject(null);
    console.log('🔄 Calling database.refreshAll()...');
    await database.refreshAll();

    // Auto-generate personas for new projects
    if (project && !editingProject) {
      console.log('🤖 Auto-generating personas for new project:', project.project_name);

      // Set the current project in the database context
      database.setCurrentProject(project);

      // Convert project data to form data format expected by generatePersonas
      const formData = {
        product_description: project.product_description,
        num_personas: project.num_personas || 3,
        industry: project.industry || "General",
        target_market: project.target_market || "Profesionales y empresas",
        business_goals: project.business_goals || "Aumentar ventas y mejorar engagement con clientes",
        competitors: project.competitors || "Competidores del sector",
        target_countries: project.target_countries || []
      };

      try {
        // Switch to form view and trigger generation
        setViewMode("form");
        console.log('🚀 Starting persona generation with data:', formData);
        await generatePersonas(formData);
      } catch (error) {
        console.error('❌ Error auto-generating personas:', error);
      }
    }
  };

  return (
    <PersonaErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Buyer Persona Generator Error:', error, errorInfo);
        // You can add error reporting here
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 relative overflow-hidden">
        <PremiumBackground />

        <div className="relative z-10 container mx-auto px-6 py-12 max-w-7xl">
          <PremiumHeader />

          {/* Projects View */}
          {viewMode === "projects" && (
            <ProjectDashboard
              onCreateProject={handleCreateProject}
              onOpenProject={(project) => loadProjectResults(project.id)}
              onEditProject={handleEditProject}
            />
          )}

          {/* Form View */}
          {viewMode === "form" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Button variant="ghost" onClick={backToProjects}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Volver a Proyectos
                </Button>
              </div>
              <PersonaFormContainer
                form={form}
                onSubmit={generatePersonas}
                isLoading={loadingState.isLoading}
                onShowHistory={() => setViewMode("projects")}
                showModeToggle={true}
                supportsDualModes={true}
              />
            </div>
          )}

          {/* Generation Loading View */}
          {viewMode === "generating" && (
            <GenerationLoading
              currentStage={loadingState.currentStage}
              progressValue={loadingState.progressValue}
              progressMessages={progressMessages}
              onCancel={cancelGeneration}
            />
          )}

          {/* Results View */}
          {viewMode === "results" && result && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <Button variant="ghost" onClick={backToForm}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Form
                </Button>
                <Button
                  onClick={() => createProjectFromGeneration(result)}
                  className="flex items-center gap-2"
                >
                  <Database className="w-4 h-4" />
                  Save to Database
                </Button>
              </div>
              <div ref={resultsRef} className="space-y-8">
                <ResultsContainer
                  result={result}
                  selectedPersonaIndex={selectedPersonaIndex}
                  premiumData={premiumData}
                  loadingPremium={loadingPremium}
                  onPersonaSelect={setSelectedPersonaIndex}
                  onLoadFeature={loadPremiumFeature}
                  onLoadAll={loadAllPremiumFeatures}
                  onOpenConversationSimulator={openConversationSimulator}
                />
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md"
            >
              <Alert variant="destructive" className="shadow-lg">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
                <button
                  onClick={() => setError(null)}
                  className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </Alert>
            </motion.div>
          )}
        </div>
      </div>

      {/* Migration Dialog */}
      <MigrationDialog
        isOpen={showMigrationDialog}
        onClose={hideMigration}
        onMigrationComplete={onMigrationComplete}
      />

      {/* Project Dialog */}
      <ProjectDialog
        isOpen={showProjectDialog}
        onClose={handleProjectDialogClose}
        project={editingProject}
        onSuccess={handleProjectSuccess}
      />

      {/* Toast Notifications */}
      <Toaster />
    </PersonaErrorBoundary>
  );
}
