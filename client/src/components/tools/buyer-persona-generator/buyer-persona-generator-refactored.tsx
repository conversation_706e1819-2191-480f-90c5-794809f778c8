/**
 * Refactored Buyer Persona Generator - Main Component
 * 
 * This is the new, modular version of the buyer persona generator
 * following best practices with proper separation of concerns.
 */

import { motion } from "framer-motion";
import { ArrowLeft, Database, RefreshCw, Users, Heart } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Components
import { PremiumBackground } from "../buyer-persona/premium-background";
import { PremiumHeader } from "../buyer-persona/premium-header";
import { PersonaFormContainer } from "./components/persona-form-container";
import { GenerationLoading } from "../buyer-persona/generation-loading";
import { ResultsContainer } from "./components/results-container";
import { PersonaErrorBoundary } from "./components/error-boundary";
import { HistoryDashboard } from "./components/history-dashboard";

// Hooks and utilities
import { usePersonaGeneratorEnhanced } from "./hooks/use-persona-generator-enhanced";
import { MigrationDialog } from "./components/migration-dialog";

export default function BuyerPersonaGeneratorRefactored() {
  const {
    // Database state
    database,

    // Generation state
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,

    // Migration state
    showMigrationDialog,
    migrationStatus,

    // Form
    form,

    // Refs
    resultsRef,

    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    backToProjects,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    setViewMode,

    // Migration actions
    checkMigrationStatus,
    showMigration,
    hideMigration,
    onMigrationComplete,

    // Project actions
    createProjectFromGeneration,
    loadProjectResults
  } = usePersonaGeneratorEnhanced();

  return (
    <PersonaErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Buyer Persona Generator Error:', error, errorInfo);
        // You can add error reporting here
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-purple-50 relative overflow-hidden">
        <PremiumBackground />

        <div className="relative z-10 container mx-auto px-6 py-12 max-w-7xl">
          <PremiumHeader />

          {/* Projects View */}
          {viewMode === "projects" && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Your Buyer Persona Projects</h2>
                  <p className="text-gray-600">Manage and organize your buyer persona generations</p>
                </div>
                <div className="flex gap-3">
                  {migrationStatus?.hasLocalData && !migrationStatus?.isCompleted && (
                    <Button variant="outline" onClick={showMigration}>
                      <Database className="w-4 h-4 mr-2" />
                      Migrate Data
                    </Button>
                  )}
                  <Button onClick={() => setViewMode("form")}>
                    Create New Project
                  </Button>
                </div>
              </div>

              {/* Projects Grid */}
              {database.projectsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
                  <span className="ml-2">Loading projects...</span>
                </div>
              ) : database.projects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {database.projects.map((project) => (
                    <Card key={project.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-lg">{project.project_name}</CardTitle>
                            <CardDescription className="line-clamp-2">
                              {project.description || project.product_description}
                            </CardDescription>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              database.toggleProjectFavorite(project.id, !project.is_favorite)
                            }}
                          >
                            <Heart className={`w-4 h-4 ${project.is_favorite ? 'fill-red-500 text-red-500' : 'text-gray-400'}`} />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{project.personas_count} personas</span>
                          <span>{project.conversations_count} conversations</span>
                        </div>
                        <div className="mt-4 flex gap-2">
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={() => loadProjectResults(project.id)}
                          >
                            Open Project
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No projects yet</h3>
                  <p className="text-gray-600 mb-4">Create your first buyer persona project to get started</p>
                  <Button onClick={() => setViewMode("form")}>
                    Create Your First Project
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Form View */}
          {viewMode === "form" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3">
                <Button variant="ghost" onClick={backToProjects}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Projects
                </Button>
              </div>
              <PersonaFormContainer
                form={form}
                onSubmit={generatePersonas}
                isLoading={loadingState.isLoading}
                onShowHistory={() => setViewMode("projects")}
              />
            </div>
          )}

          {/* Generation Loading View */}
          {viewMode === "generating" && (
            <GenerationLoading
              currentStage={loadingState.currentStage}
              progressValue={loadingState.progressValue}
              progressMessages={progressMessages}
              onCancel={cancelGeneration}
            />
          )}

          {/* Results View */}
          {viewMode === "results" && result && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <Button variant="ghost" onClick={backToForm}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Form
                </Button>
                <Button
                  onClick={() => createProjectFromGeneration(result)}
                  className="flex items-center gap-2"
                >
                  <Database className="w-4 h-4" />
                  Save to Database
                </Button>
              </div>
              <div ref={resultsRef} className="space-y-8">
                <ResultsContainer
                  result={result}
                  selectedPersonaIndex={selectedPersonaIndex}
                  premiumData={premiumData}
                  loadingPremium={loadingPremium}
                  onPersonaSelect={setSelectedPersonaIndex}
                  onLoadFeature={loadPremiumFeature}
                  onLoadAll={loadAllPremiumFeatures}
                  onOpenConversationSimulator={openConversationSimulator}
                />
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md"
            >
              <Alert variant="destructive" className="shadow-lg">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
                <button
                  onClick={() => setError(null)}
                  className="absolute top-2 right-2 text-red-600 hover:text-red-800"
                >
                  ×
                </button>
              </Alert>
            </motion.div>
          )}
        </div>
      </div>

      {/* Migration Dialog */}
      <MigrationDialog
        isOpen={showMigrationDialog}
        onClose={hideMigration}
        onMigrationComplete={onMigrationComplete}
      />
    </PersonaErrorBoundary>
  );
}
