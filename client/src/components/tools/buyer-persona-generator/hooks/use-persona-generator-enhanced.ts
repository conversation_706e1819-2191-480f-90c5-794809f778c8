/**
 * Enhanced Buyer Persona Generator Hook
 * Combines database functionality with generation logic and migration support
 */

import { useState, useRef, useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useLocation } from 'wouter'

import { usePersonaDatabase } from './use-persona-database'
import { PersonaApiService } from '../utils/api-service'
import { PersonaMigrationManager } from '../utils/migration-manager'
import type { GenerationResult, PremiumFeatureType, PremiumFeatureData } from '../types'

// Form validation schema
const formSchema = z.object({
  product_description: z.string().min(10, 'Product description must be at least 10 characters'),
  num_personas: z.number().min(1).max(10).default(3),
  industry: z.string().optional(),
  target_market: z.string().optional(),
  business_goals: z.string().optional(),
  competitors: z.string().optional(),
  target_countries: z.array(z.string()).default([])
})

type FormData = z.infer<typeof formSchema>

interface LoadingState {
  isLoading: boolean
  currentStage: string
  progressValue: number
}

interface UsePersonaGeneratorEnhancedReturn {
  // Database state
  database: ReturnType<typeof usePersonaDatabase>
  
  // Generation state
  viewMode: 'projects' | 'form' | 'generating' | 'results'
  result: GenerationResult | null
  selectedPersonaIndex: number
  error: string | null
  loadingState: LoadingState
  premiumData: PremiumFeatureData | null
  loadingPremium: PremiumFeatureType | null
  progressMessages: string[]
  
  // Migration state
  showMigrationDialog: boolean
  migrationStatus: any
  
  // Form
  form: ReturnType<typeof useForm<FormData>>
  
  // Refs
  resultsRef: React.RefObject<HTMLDivElement>
  
  // Actions
  generatePersonas: (values: FormData) => Promise<void>
  loadPremiumFeature: (featureType: PremiumFeatureType) => Promise<void>
  loadAllPremiumFeatures: () => Promise<void>
  cancelGeneration: () => void
  backToForm: () => void
  backToProjects: () => void
  openConversationSimulator: (personaIndex?: number) => void
  setSelectedPersonaIndex: (index: number) => void
  setError: (error: string | null) => void
  setViewMode: (mode: 'projects' | 'form' | 'generating' | 'results') => void
  
  // Migration actions
  checkMigrationStatus: () => Promise<void>
  showMigration: () => void
  hideMigration: () => void
  onMigrationComplete: () => void
  
  // Project actions
  createProjectFromGeneration: (result: GenerationResult, projectName?: string) => Promise<void>
  loadProjectResults: (projectId: string) => Promise<void>
}

export function usePersonaGeneratorEnhanced(): UsePersonaGeneratorEnhancedReturn {
  // Database hook
  const database = usePersonaDatabase()
  
  // Navigation
  const [, setLocation] = useLocation()
  
  // Generation state
  const [viewMode, setViewMode] = useState<'projects' | 'form' | 'generating' | 'results'>('projects')
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [selectedPersonaIndex, setSelectedPersonaIndex] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    currentStage: '',
    progressValue: 0
  })
  const [premiumData, setPremiumData] = useState<PremiumFeatureData | null>(null)
  const [loadingPremium, setLoadingPremium] = useState<PremiumFeatureType | null>(null)
  const [progressMessages, setProgressMessages] = useState<string[]>([])
  
  // Migration state
  const [showMigrationDialog, setShowMigrationDialog] = useState(false)
  const [migrationStatus, setMigrationStatus] = useState<any>(null)
  
  // Form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      product_description: '',
      num_personas: 3,
      industry: '',
      target_market: '',
      business_goals: '',
      competitors: '',
      target_countries: []
    }
  })
  
  // Refs
  const resultsRef = useRef<HTMLDivElement>(null)
  
  // Check migration status on mount
  useEffect(() => {
    checkMigrationStatus()
  }, [])
  
  const checkMigrationStatus = useCallback(async () => {
    try {
      const status = await PersonaMigrationManager.getMigrationStatus()
      setMigrationStatus(status)
      
      // Show migration dialog if there's local data and migration not completed
      if (status.hasLocalData && !status.isCompleted) {
        setShowMigrationDialog(true)
      }
    } catch (error) {
      console.error('Error checking migration status:', error)
    }
  }, [])
  
  const showMigration = useCallback(() => {
    setShowMigrationDialog(true)
  }, [])
  
  const hideMigration = useCallback(() => {
    setShowMigrationDialog(false)
  }, [])
  
  const onMigrationComplete = useCallback(() => {
    setShowMigrationDialog(false)
    database.refreshAll()
    checkMigrationStatus()
  }, [database])
  
  // Generate personas
  const generatePersonas = useCallback(async (values: FormData) => {
    setError(null)
    setLoadingState({
      isLoading: true,
      currentStage: 'Preparing generation...',
      progressValue: 0
    })
    setViewMode('generating')
    setProgressMessages([])
    
    try {
      // Simulate progress updates
      const progressStages = [
        'Analyzing product description...',
        'Generating buyer personas...',
        'Creating detailed profiles...',
        'Finalizing recommendations...'
      ]
      
      let currentProgress = 0
      const progressInterval = setInterval(() => {
        currentProgress += 20
        if (currentProgress <= 80) {
          setLoadingState(prev => ({
            ...prev,
            progressValue: currentProgress,
            currentStage: progressStages[Math.floor(currentProgress / 20)] || 'Processing...'
          }))
        }
      }, 1000)
      
      const generationResult = await PersonaApiService.generatePersonas(values)
      
      clearInterval(progressInterval)
      setLoadingState(prev => ({
        ...prev,
        progressValue: 100,
        currentStage: 'Complete!'
      }))
      
      setResult(generationResult)
      setViewMode('results')
      
      // Scroll to results after delay
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 500)
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate buyer personas'
      setError(errorMessage)
      setViewMode('form')
    } finally {
      setLoadingState({
        isLoading: false,
        currentStage: '',
        progressValue: 0
      })
    }
  }, [])
  
  // Load premium feature
  const loadPremiumFeature = useCallback(async (featureType: PremiumFeatureType) => {
    if (!result?.buyer_personas?.[selectedPersonaIndex]) return
    
    setLoadingPremium(featureType)
    
    try {
      const persona = result.buyer_personas[selectedPersonaIndex]
      const productDescription = result.original_product_description || form.getValues().product_description
      
      const featureData = await PersonaApiService.loadPremiumFeature(
        featureType,
        persona,
        productDescription
      )
      
      setPremiumData(prev => ({
        ...prev,
        [featureType]: featureData
      }))
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to load ${featureType}`
      setError(errorMessage)
    } finally {
      setLoadingPremium(null)
    }
  }, [result, selectedPersonaIndex, form])
  
  // Load all premium features
  const loadAllPremiumFeatures = useCallback(async () => {
    const features: PremiumFeatureType[] = ['avatars', 'conversations', 'insights', 'campaigns']
    
    for (const feature of features) {
      await loadPremiumFeature(feature)
    }
  }, [loadPremiumFeature])
  
  // Cancel generation
  const cancelGeneration = useCallback(() => {
    setLoadingState({
      isLoading: false,
      currentStage: '',
      progressValue: 0
    })
    setViewMode('form')
  }, [])
  
  // Navigation actions
  const backToForm = useCallback(() => {
    setViewMode('form')
    setResult(null)
    setPremiumData(null)
    setError(null)
  }, [])
  
  const backToProjects = useCallback(() => {
    setViewMode('projects')
    setResult(null)
    setPremiumData(null)
    setError(null)
  }, [])
  
  // Open conversation simulator
  const openConversationSimulator = useCallback((personaIndex?: number) => {
    if (!result?.buyer_personas) return
    
    const targetPersonaIndex = personaIndex ?? selectedPersonaIndex
    const persona = result.buyer_personas[targetPersonaIndex]
    
    if (!persona) return
    
    // Convert persona name to URL-safe ID
    const personaId = persona.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
    
    // Navigate to conversation simulator
    setLocation(`/dashboard/herramientas/buyer-persona-generator/simulador/${personaId}`)
  }, [result, selectedPersonaIndex, setLocation])
  
  // Create project from generation
  const createProjectFromGeneration = useCallback(async (generationResult: GenerationResult, projectName?: string) => {
    try {
      const projectData = {
        project_name: projectName || `Buyer Personas - ${new Date().toLocaleDateString()}`,
        description: 'Generated buyer personas project',
        product_description: generationResult.original_product_description || 'No description available',
        industry: 'General',
        target_market: 'General Market',
        business_goals: 'Improve customer understanding',
        num_personas: generationResult.buyer_personas?.length || 3,
        tags: ['generated']
      }
      
      const project = await database.createProject(projectData)
      
      // Create personas
      if (generationResult.buyer_personas) {
        for (const persona of generationResult.buyer_personas) {
          const personaData = {
            project_id: project.id,
            persona_name: persona.name,
            age: persona.age,
            gender: persona.gender,
            location: persona.location,
            education: persona.education,
            income_level: persona.income_level,
            marital_status: persona.marital_status,
            job_info: persona.job || {},
            personal_background: persona.personal_background,
            goals: persona.goals || [],
            challenges: persona.challenges || [],
            objections: persona.objections || [],
            communication_channels: persona.communication_channels || [],
            influences: persona.influences || [],
            quotes: persona.quotes || [],
            typical_day: persona.typical_day,
            brand_affinities: persona.brand_affinities || [],
            buying_process: persona.buying_process || {},
            avatar_description: persona.avatar_description,
            avatar_url: persona.avatar_url,
            avatar_id: persona.avatar_id,
            generation_prompt: 'Generated from form',
            ai_model_used: generationResult.ai_model_used || 'Unknown',
            generation_timestamp: generationResult.generation_timestamp?.toString() || new Date().toISOString()
          }
          
          await database.createPersona(personaData)
        }
      }
      
      // Refresh projects list
      await database.loadProjects()
      
    } catch (error) {
      console.error('Error creating project from generation:', error)
      throw error
    }
  }, [database])
  
  // Load project results
  const loadProjectResults = useCallback(async (projectId: string) => {
    try {
      await database.loadProject(projectId)
      await database.loadPersonasByProject(projectId)
      setViewMode('projects')
    } catch (error) {
      console.error('Error loading project results:', error)
      setError('Failed to load project')
    }
  }, [database])
  
  return {
    // Database state
    database,
    
    // Generation state
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,
    
    // Migration state
    showMigrationDialog,
    migrationStatus,
    
    // Form
    form,
    
    // Refs
    resultsRef,
    
    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    backToProjects,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    setViewMode,
    
    // Migration actions
    checkMigrationStatus,
    showMigration,
    hideMigration,
    onMigrationComplete,
    
    // Project actions
    createProjectFromGeneration,
    loadProjectResults
  }
}
