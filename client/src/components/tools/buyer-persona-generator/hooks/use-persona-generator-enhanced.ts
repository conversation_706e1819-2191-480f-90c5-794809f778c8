/**
 * Enhanced Buyer Persona Generator Hook
 * Combines database functionality with generation logic and migration support
 */

import { useState, useRef, useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useLocation } from 'wouter'

import { usePersonaDatabase } from './use-persona-database'
import { PersonaApiService } from '../utils/api-service'
import { PersonaMigrationManager } from '../utils/migration-manager'
import type { GenerationResult, PremiumFeatureType, PremiumFeatureData } from '../types'

// Form validation schema
const formSchema = z.object({
  product_description: z.string().min(10, 'Product description must be at least 10 characters'),
  num_personas: z.number().min(1).max(10).default(3),
  industry: z.string().optional(),
  target_market: z.string().optional(),
  business_goals: z.string().optional(),
  competitors: z.string().optional(),
  target_countries: z.array(z.string()).default([])
})

type FormData = z.infer<typeof formSchema>

interface LoadingState {
  isLoading: boolean
  currentStage: string
  progressValue: number
}

interface UsePersonaGeneratorEnhancedReturn {
  // Database state
  database: ReturnType<typeof usePersonaDatabase>
  
  // Generation state
  viewMode: 'projects' | 'form' | 'generating' | 'results'
  result: GenerationResult | null
  selectedPersonaIndex: number
  error: string | null
  loadingState: LoadingState
  premiumData: PremiumFeatureData | null
  loadingPremium: PremiumFeatureType | null
  progressMessages: string[]
  
  // Migration state
  showMigrationDialog: boolean
  migrationStatus: any
  
  // Form
  form: ReturnType<typeof useForm<FormData>>
  
  // Refs
  resultsRef: React.RefObject<HTMLDivElement>
  
  // Actions
  generatePersonas: (values: FormData) => Promise<void>
  loadPremiumFeature: (featureType: PremiumFeatureType) => Promise<void>
  loadAllPremiumFeatures: () => Promise<void>
  cancelGeneration: () => void
  backToForm: () => void
  backToProjects: () => void
  openConversationSimulator: (personaIndex?: number) => void
  setSelectedPersonaIndex: (index: number) => void
  setError: (error: string | null) => void
  setViewMode: (mode: 'projects' | 'form' | 'generating' | 'results') => void
  
  // Migration actions
  checkMigrationStatus: () => Promise<void>
  showMigration: () => void
  hideMigration: () => void
  onMigrationComplete: () => void
  
  // Project actions (manual save removed - now automatic)
  loadProjectResults: (projectId: string) => Promise<void>
}

export function usePersonaGeneratorEnhanced(): UsePersonaGeneratorEnhancedReturn {
  // Database hook
  const database = usePersonaDatabase()
  
  // Navigation
  const [, setLocation] = useLocation()
  
  // Generation state
  const [viewMode, setViewMode] = useState<'projects' | 'form' | 'generating' | 'results'>('projects')
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [selectedPersonaIndex, setSelectedPersonaIndex] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    currentStage: '',
    progressValue: 0
  })
  const [premiumData, setPremiumData] = useState<PremiumFeatureData | null>(null)
  const [loadingPremium, setLoadingPremium] = useState<PremiumFeatureType | null>(null)
  const [progressMessages, setProgressMessages] = useState<string[]>([])
  
  // Migration state
  const [showMigrationDialog, setShowMigrationDialog] = useState(false)
  const [migrationStatus, setMigrationStatus] = useState<any>(null)
  
  // Form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      product_description: '',
      num_personas: 3,
      industry: '',
      target_market: '',
      business_goals: '',
      competitors: '',
      target_countries: []
    }
  })
  
  // Refs
  const resultsRef = useRef<HTMLDivElement>(null)
  
  // Check migration status on mount
  useEffect(() => {
    checkMigrationStatus()
  }, [])
  
  const checkMigrationStatus = useCallback(async () => {
    try {
      const status = await PersonaMigrationManager.getMigrationStatus()
      setMigrationStatus(status)
      
      // Show migration dialog if there's local data and migration not completed
      if (status.hasLocalData && !status.isCompleted) {
        setShowMigrationDialog(true)
      }
    } catch (error) {
      console.error('Error checking migration status:', error)
    }
  }, [])
  
  const showMigration = useCallback(() => {
    setShowMigrationDialog(true)
  }, [])
  
  const hideMigration = useCallback(() => {
    setShowMigrationDialog(false)
  }, [])
  
  const onMigrationComplete = useCallback(() => {
    setShowMigrationDialog(false)
    database.refreshAll()
    checkMigrationStatus()
  }, [database])

  // Save generated personas to database
  const saveGeneratedPersonasToDatabase = useCallback(async (generationResult: GenerationResult, projectId: string) => {
    try {
      console.log('💾 Saving personas to database for project:', projectId)

      for (const persona of generationResult.buyer_personas) {
        const personaData = {
          project_id: projectId,
          persona_name: persona.name,
          age: persona.age,
          gender: persona.gender,
          location: persona.location,
          education: persona.education,
          income_level: persona.income_level,
          marital_status: persona.marital_status,
          job_info: {
            title: persona.job?.title || '',
            industry: persona.job?.industry || '',
            company_size: persona.job?.company_size || '',
            experience_level: persona.job?.experience_level || ''
          },
          goals: persona.goals,
          challenges: persona.challenges,
          objections: persona.objections,
          communication_channels: persona.communication_channels,
          influences: persona.influences,
          quotes: persona.quotes,
          buying_process: {
            awareness_stage: persona.buying_process?.awareness_stage || '',
            consideration_stage: persona.buying_process?.consideration_stage || '',
            decision_stage: persona.buying_process?.decision_stage || '',
            post_purchase: persona.buying_process?.post_purchase || ''
          },
          avatar_description: persona.avatar_description,
          generation_prompt: generationResult.generation_prompt || '',
          ai_model_used: generationResult.ai_model_used || 'gemini-1.5-flash',
          generation_timestamp: new Date().toISOString()
        }

        console.log('💾 Creating persona:', persona.name)
        await database.createPersona(personaData)
      }

      console.log('✅ All personas saved to database')

      // Update project personas count
      await database.updateProject(projectId, {
        personas_count: generationResult.buyer_personas.length,
        last_accessed_at: new Date().toISOString()
      })

    } catch (error) {
      console.error('❌ Error saving personas to database:', error)
      // Don't throw error to avoid breaking the generation flow
    }
  }, [database])

  // Auto-save generation results to database
  const autoSaveGenerationResults = useCallback(async (generationResult: GenerationResult, projectId: string) => {
    try {
      console.log('🔄 Starting auto-save process...')

      // 1. Save generation prompt for future regeneration
      if (generationResult.original_product_description) {
        const promptData = {
          project_id: projectId,
          prompt_name: `Generation ${new Date().toLocaleDateString()}`,
          prompt_content: generationResult.original_product_description,
          prompt_parameters: {
            num_personas: generationResult.buyer_personas?.length || 3,
            industry: values.industry,
            target_market: values.target_market,
            business_goals: values.business_goals,
            competitors: values.competitors,
            target_countries: values.target_countries
          },
          generation_results: generationResult,
          personas_generated: generationResult.buyer_personas?.length || 0,
          ai_model_used: generationResult.ai_model_used || 'unknown'
        }

        await database.saveGenerationPrompt(promptData)
        console.log('✅ Generation prompt saved')
      }

      // 2. Save individual personas with avatar images
      if (generationResult.buyer_personas) {
        for (const persona of generationResult.buyer_personas) {
          // Save avatar image if it exists (following Visual Complexity Analyzer pattern)
          let avatarUrl = persona.avatar_url
          if (persona.avatar_url && persona.avatar_url.startsWith('data:')) {
            try {
              avatarUrl = await database.uploadAvatarImage(persona.avatar_url, persona.name)
              console.log(`✅ Avatar uploaded for ${persona.name}`)
            } catch (error) {
              console.warn(`⚠️ Failed to upload avatar for ${persona.name}:`, error)
            }
          }

          // Save persona to database
          const personaData = {
            project_id: projectId,
            persona_name: persona.name,
            age: persona.age,
            gender: persona.gender,
            location: persona.location,
            education: persona.education,
            income_level: persona.income_level,
            marital_status: persona.marital_status,
            job_info: persona.job,
            personal_background: persona.personal_background,
            goals: persona.goals,
            challenges: persona.challenges,
            objections: persona.objections,
            communication_channels: persona.communication_channels,
            influences: persona.influences,
            quotes: persona.quotes,
            typical_day: persona.typical_day,
            brand_affinities: persona.brand_affinities,
            buying_process: persona.buying_process,
            avatar_description: persona.avatar_description,
            avatar_url: avatarUrl,
            avatar_id: persona.avatar_id,
            generation_prompt: generationResult.original_product_description,
            ai_model_used: generationResult.ai_model_used,
            generation_timestamp: new Date().toISOString()
          }

          await database.createPersona(personaData)
          console.log(`✅ Persona saved: ${persona.name}`)
        }
      }

      // 3. Update project with latest access time
      await database.updateProject(projectId, {
        last_accessed_at: new Date().toISOString(),
        personas_count: generationResult.buyer_personas?.length || 0
      })

      console.log('🎉 Auto-save completed successfully')
      setProgressMessages(prev => [...prev, '💾 All data saved automatically to database'])
      setProgressMessages(prev => [...prev, '🧹 Project cleanup completed'])
      setProgressMessages(prev => [...prev, '✅ Ready to view results!'])

    } catch (error) {
      console.error('❌ Auto-save failed:', error)
      setProgressMessages(prev => [...prev, '⚠️ Auto-save failed, but generation completed'])
    }
  }, [database, values])

  // Generate personas
  const generatePersonas = useCallback(async (values: FormData) => {
    setError(null)
    setLoadingState({
      isLoading: true,
      currentStage: 'Creating project...',
      progressValue: 0
    })
    setViewMode('generating')
    setProgressMessages([])

    try {
      // Step 1: Create project first using form data
      console.log('📝 Creating project from generation form data...')

      // Generate a descriptive project name based on industry and target market
      const industryText = values.industry || 'General'
      const targetText = values.target_market || 'Market'
      const timestamp = new Date().toLocaleDateString('es-ES')
      const projectName = `${industryText} - ${targetText} (${timestamp})`

      const projectData = {
        project_name: projectName,
        description: `Proyecto generado automáticamente para ${industryText}`,
        product_description: values.product_description,
        industry: values.industry || 'General',
        target_market: values.target_market || 'General Market',
        business_goals: values.business_goals || 'Mejorar comprensión del cliente',
        competitors: values.competitors || '',
        target_countries: values.target_countries || [],
        num_personas: values.num_personas || 3,
        tags: ['generado-automaticamente']
      }

      const createdProject = await database.createProject(projectData)
      console.log('✅ Project created:', createdProject.project_name)

      // Set as current project for persona saving
      database.setCurrentProject(createdProject)

      // Add success message to progress
      setProgressMessages(prev => [...prev, `✅ Proyecto "${createdProject.project_name}" creado exitosamente`])

      // Step 2: Check API health before generation
      setLoadingState({
        isLoading: true,
        currentStage: 'Checking service availability...',
        progressValue: 20
      })

      try {
        const healthCheck = await PersonaApiService.checkHealth()
        if (healthCheck.status !== 'healthy') {
          console.warn('API health check failed:', healthCheck.details)
          setProgressMessages(prev => [...prev, `⚠️ Service status: ${healthCheck.status}`])
        } else {
          setProgressMessages(prev => [...prev, `✅ Service is healthy and ready`])
        }
      } catch (healthError) {
        console.warn('Health check failed:', healthError)
        setProgressMessages(prev => [...prev, `⚠️ Could not verify service health`])
      }

      // Step 3: Update loading state for persona generation
      setLoadingState({
        isLoading: true,
        currentStage: 'Preparing generation...',
        progressValue: 30
      })

      // Enhanced progress updates with auto-save indicators
      const progressStages = [
        'Analyzing product description...',
        'Generating buyer personas...',
        'Creating detailed profiles...',
        'Auto-saving to database...'
      ]

      let currentProgress = 30 // Start from 30% since project creation and health check are done
      const progressInterval = setInterval(() => {
        currentProgress += 15
        if (currentProgress <= 85) {
          setLoadingState(prev => ({
            ...prev,
            progressValue: currentProgress,
            currentStage: progressStages[Math.floor((currentProgress - 30) / 15)] || 'Processing...'
          }))
        }
      }, 1000)
      
      const generationResult = await PersonaApiService.generatePersonas(values)
      
      clearInterval(progressInterval)
      setLoadingState(prev => ({
        ...prev,
        progressValue: 100,
        currentStage: 'Complete!'
      }))
      
      setResult(generationResult)
      setViewMode('results')

      // Automatically save everything to database
      console.log('💾 Auto-saving generation results to database...')
      await autoSaveGenerationResults(generationResult, createdProject.id)

      // Update project statistics
      await database.updateProjectPersonasCount(createdProject.id)

      // Auto-cleanup old projects (keep only 5 most recent)
      console.log('🧹 Running auto-cleanup for old projects...')
      await database.autoCleanupOldProjects()

      // Refresh project list to reflect changes
      await database.refreshAll()

      // Scroll to results after delay
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 500)
      
    } catch (err) {
      console.error('❌ Persona generation failed:', err)

      let errorMessage = 'Failed to generate buyer personas'
      if (err instanceof Error) {
        errorMessage = err.message

        // Provide specific guidance based on error type
        if (err.message.includes('500')) {
          errorMessage += '\n\nThis appears to be a server issue. The system will automatically retry with a backup service.'
        } else if (err.message.includes('503')) {
          errorMessage += '\n\nThe AI service is temporarily unavailable. Please try again in a few moments.'
        } else if (err.message.includes('401') || err.message.includes('authentication')) {
          errorMessage += '\n\nThere was an authentication issue with the AI service. Please contact support.'
        }
      }

      setError(errorMessage)
      setViewMode('form')

      // Add error to progress messages for debugging
      setProgressMessages(prev => [...prev, `❌ Error: ${errorMessage}`])
    } finally {
      setLoadingState({
        isLoading: false,
        currentStage: '',
        progressValue: 0
      })
    }
  }, [])
  
  // Load premium feature
  const loadPremiumFeature = useCallback(async (featureType: PremiumFeatureType) => {
    if (!result?.buyer_personas?.[selectedPersonaIndex]) return
    
    setLoadingPremium(featureType)
    
    try {
      const persona = result.buyer_personas[selectedPersonaIndex]
      const productDescription = result.original_product_description || form.getValues().product_description
      
      const featureData = await PersonaApiService.loadPremiumFeature(
        featureType,
        persona,
        productDescription
      )
      
      setPremiumData(prev => ({
        ...prev,
        [featureType]: featureData
      }))
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to load ${featureType}`
      setError(errorMessage)
    } finally {
      setLoadingPremium(null)
    }
  }, [result, selectedPersonaIndex, form])
  
  // Load all premium features
  const loadAllPremiumFeatures = useCallback(async () => {
    const features: PremiumFeatureType[] = ['avatars', 'conversations', 'insights', 'campaigns']
    
    for (const feature of features) {
      await loadPremiumFeature(feature)
    }
  }, [loadPremiumFeature])
  
  // Cancel generation
  const cancelGeneration = useCallback(() => {
    setLoadingState({
      isLoading: false,
      currentStage: '',
      progressValue: 0
    })
    setViewMode('form')
  }, [])
  
  // Navigation actions
  const backToForm = useCallback(() => {
    setViewMode('form')
    setResult(null)
    setPremiumData(null)
    setError(null)
  }, [])
  
  const backToProjects = useCallback(() => {
    setViewMode('projects')
    setResult(null)
    setPremiumData(null)
    setError(null)
    // Refresh projects list to show newly created projects
    database.refreshAll()
  }, [database])
  
  // Open conversation simulator
  const openConversationSimulator = useCallback((personaIndex?: number) => {
    if (!result?.buyer_personas) return
    
    const targetPersonaIndex = personaIndex ?? selectedPersonaIndex
    const persona = result.buyer_personas[targetPersonaIndex]
    
    if (!persona) return
    
    // Convert persona name to URL-safe ID
    const personaId = persona.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
    
    // Navigate to conversation simulator
    setLocation(`/dashboard/herramientas/buyer-persona-generator/simulador/${personaId}`)
  }, [result, selectedPersonaIndex, setLocation])

  // Manual save function removed - now using automatic database persistence

  // Load project results
  const loadProjectResults = useCallback(async (projectId: string) => {
    try {
      await database.loadProject(projectId)
      await database.loadPersonasByProject(projectId)
      setViewMode('projects')
    } catch (error) {
      console.error('Error loading project results:', error)
      setError('Failed to load project')
    }
  }, [database])
  
  return {
    // Database state
    database,
    
    // Generation state
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,
    
    // Migration state
    showMigrationDialog,
    migrationStatus,
    
    // Form
    form,
    
    // Refs
    resultsRef,
    
    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    backToProjects,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    setViewMode,
    
    // Migration actions
    checkMigrationStatus,
    showMigration,
    hideMigration,
    onMigrationComplete,

    // Project actions (manual save removed - now automatic)
    loadProjectResults
  }
}
