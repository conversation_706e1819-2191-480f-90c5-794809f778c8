import { supabase } from '@/lib/supabase'

/**
 * Buyer Persona Service - Database operations for buyer persona generator
 * Following established patterns from Visual Complexity Analyzer and Focus Group tools
 */

// Types for database operations
export interface BuyerPersonaProject {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  project_name: string
  description?: string
  product_description: string
  industry?: string
  target_market?: string
  business_goals?: string
  competitors?: string
  target_countries: string[]
  num_personas: number
  project_status: 'active' | 'archived' | 'draft'
  is_favorite: boolean
  tags: string[]
  notes?: string
  personas_count: number
  conversations_count: number
  last_accessed_at?: string
  view_count: number
}

export interface BuyerPersona {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  project_id: string
  persona_name: string
  age: number
  gender?: string
  location?: string
  education?: string
  income_level?: string
  marital_status?: string
  job_info: any // JSONB
  personal_background?: string
  goals: string[]
  challenges: string[]
  objections: string[]
  communication_channels: string[]
  influences: string[]
  quotes: string[]
  typical_day?: string
  brand_affinities: string[]
  buying_process: any // JSONB
  avatar_description?: string
  avatar_url?: string
  avatar_id?: string
  generation_prompt?: string
  ai_model_used?: string
  generation_timestamp?: string
  is_favorite: boolean
  custom_name?: string
  notes?: string
  conversation_count: number
  last_conversation_at?: string
  view_count: number
}

export interface BuyerPersonaConversation {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  project_id: string
  persona_id: string
  conversation_title: string
  conversation_context?: string
  messages: any[] // JSONB array
  total_messages: number
  conversation_duration_minutes?: number
  conversation_status: 'active' | 'paused' | 'completed' | 'archived'
  conversation_summary?: string
  key_insights: string[]
  sentiment_analysis?: any // JSONB
  is_favorite: boolean
  tags: string[]
  notes?: string
  view_count: number
  last_message_at?: string
}

export interface BuyerPersonaGenerationPrompt {
  id: string
  created_at: string
  updated_at: string
  user_id: string
  project_id: string
  prompt_name: string
  prompt_description?: string
  base_prompt: string
  prompt_parameters: any // JSONB
  generation_results?: any // JSONB
  personas_generated: number
  generation_success: boolean
  generation_error?: string
  ai_model_used?: string
  generation_duration_ms?: number
  generation_timestamp?: string
  is_favorite: boolean
  is_template: boolean
  usage_count: number
  last_used_at?: string
}

// Create data types
export interface CreateBuyerPersonaProjectData {
  project_name: string
  description?: string
  product_description: string
  industry?: string
  target_market?: string
  business_goals?: string
  competitors?: string
  target_countries?: string[]
  num_personas?: number
  tags?: string[]
  notes?: string
}

export interface CreateBuyerPersonaData {
  project_id: string
  persona_name: string
  age: number
  gender?: string
  location?: string
  education?: string
  income_level?: string
  marital_status?: string
  job_info: any
  personal_background?: string
  goals?: string[]
  challenges?: string[]
  objections?: string[]
  communication_channels?: string[]
  influences?: string[]
  quotes?: string[]
  typical_day?: string
  brand_affinities?: string[]
  buying_process: any
  avatar_description?: string
  avatar_url?: string
  avatar_id?: string
  generation_prompt?: string
  ai_model_used?: string
  generation_timestamp?: string
  custom_name?: string
  notes?: string
}

export interface CreateBuyerPersonaConversationData {
  project_id: string
  persona_id: string
  conversation_title: string
  conversation_context?: string
  messages?: any[]
  tags?: string[]
  notes?: string
}

export interface CreateBuyerPersonaGenerationPromptData {
  project_id: string
  prompt_name: string
  prompt_description?: string
  base_prompt: string
  prompt_parameters: any
  generation_results?: any
  personas_generated?: number
  generation_success?: boolean
  generation_error?: string
  ai_model_used?: string
  generation_duration_ms?: number
  generation_timestamp?: string
  is_template?: boolean
}

// Update data types
export interface UpdateBuyerPersonaProjectData {
  id: string
  project_name?: string
  description?: string
  product_description?: string
  industry?: string
  target_market?: string
  business_goals?: string
  competitors?: string
  target_countries?: string[]
  num_personas?: number
  project_status?: 'active' | 'archived' | 'draft'
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  last_accessed_at?: string
}

export interface UpdateBuyerPersonaData {
  id: string
  persona_name?: string
  age?: number
  gender?: string
  location?: string
  education?: string
  income_level?: string
  marital_status?: string
  job_info?: any
  personal_background?: string
  goals?: string[]
  challenges?: string[]
  objections?: string[]
  communication_channels?: string[]
  influences?: string[]
  quotes?: string[]
  typical_day?: string
  brand_affinities?: string[]
  buying_process?: any
  avatar_description?: string
  avatar_url?: string
  avatar_id?: string
  is_favorite?: boolean
  custom_name?: string
  notes?: string
  last_conversation_at?: string
}

export interface UpdateBuyerPersonaConversationData {
  id: string
  conversation_title?: string
  conversation_context?: string
  messages?: any[]
  total_messages?: number
  conversation_duration_minutes?: number
  conversation_status?: 'active' | 'paused' | 'completed' | 'archived'
  conversation_summary?: string
  key_insights?: string[]
  sentiment_analysis?: any
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  last_message_at?: string
}

/**
 * Service class for buyer persona database operations
 */
export class BuyerPersonaService {
  /**
   * Get current authenticated user
   */
  private async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser()
    if (error || !user) {
      throw new Error('User not authenticated')
    }
    return user
  }

  // ===============================================
  // PROJECT OPERATIONS
  // ===============================================

  /**
   * Get all buyer persona projects for the current user
   */
  async getProjects(): Promise<BuyerPersonaProject[]> {
    const user = await this.getCurrentUser()
    console.log('👤 Current user for getProjects:', user.id)

    const { data, error } = await supabase
      .from('buyer_persona_projects')
      .select('*')
      .eq('user_id', user.id)
      .order('last_accessed_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Error fetching buyer persona projects:', error)
      throw new Error(`Failed to fetch projects: ${error.message}`)
    }

    console.log('📊 Raw projects data from DB:', data)
    return data || []
  }

  /**
   * Get a specific project by ID
   */
  async getProjectById(id: string): Promise<BuyerPersonaProject | null> {
    const user = await this.getCurrentUser()
    
    const { data, error } = await supabase
      .from('buyer_persona_projects')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      throw new Error(`Failed to fetch project: ${error.message}`)
    }

    return data
  }

  /**
   * Create a new buyer persona project
   */
  async createProject(projectData: CreateBuyerPersonaProjectData): Promise<BuyerPersonaProject> {
    const user = await this.getCurrentUser()
    console.log('👤 Current user for createProject:', user.id)

    const insertData = {
      user_id: user.id,
      project_name: projectData.project_name,
      description: projectData.description,
      product_description: projectData.product_description,
      industry: projectData.industry,
      target_market: projectData.target_market,
      business_goals: projectData.business_goals,
      competitors: projectData.competitors,
      target_countries: projectData.target_countries || [],
      num_personas: projectData.num_personas || 3,
      project_status: 'active' as const,
      is_favorite: false,
      tags: projectData.tags || [],
      notes: projectData.notes,
      personas_count: 0,
      conversations_count: 0,
      view_count: 0,
      last_accessed_at: new Date().toISOString()
    }

    console.log('📝 Insert data for new project:', insertData)

    const { data, error } = await supabase
      .from('buyer_persona_projects')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      console.error('❌ Error creating buyer persona project:', error)
      throw new Error(`Failed to create project: ${error.message}`)
    }

    console.log('✅ Project created in database:', data)
    return data
  }

  /**
   * Update a buyer persona project
   */
  async updateProject(updateData: UpdateBuyerPersonaProjectData): Promise<BuyerPersonaProject> {
    const user = await this.getCurrentUser()
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .from('buyer_persona_projects')
      .update(updates)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating buyer persona project:', error)
      throw new Error(`Failed to update project: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a buyer persona project
   */
  async deleteProject(id: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { error } = await supabase
      .from('buyer_persona_projects')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting buyer persona project:', error)
      throw new Error(`Failed to delete project: ${error.message}`)
    }
  }

  /**
   * Toggle favorite status for a project
   */
  async toggleProjectFavorite(id: string, isFavorite: boolean): Promise<BuyerPersonaProject> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_persona_projects')
      .update({ is_favorite: isFavorite })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling project favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  /**
   * Update project access timestamp
   */
  async updateProjectAccess(id: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { error } = await supabase
      .from('buyer_persona_projects')
      .update({
        last_accessed_at: new Date().toISOString(),
        view_count: supabase.sql`view_count + 1`
      })
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error updating project access:', error)
      // Don't throw error for access tracking
    }
  }

  // ===============================================
  // PERSONA OPERATIONS
  // ===============================================

  /**
   * Get all buyer personas for a project
   */
  async getPersonasByProject(projectId: string): Promise<BuyerPersona[]> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_personas')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching buyer personas:', error)
      throw new Error(`Failed to fetch personas: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get a specific persona by ID
   */
  async getPersonaById(id: string): Promise<BuyerPersona | null> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_personas')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      throw new Error(`Failed to fetch persona: ${error.message}`)
    }

    return data
  }

  /**
   * Create a new buyer persona
   */
  async createPersona(personaData: CreateBuyerPersonaData): Promise<BuyerPersona> {
    const user = await this.getCurrentUser()

    const insertData = {
      user_id: user.id,
      project_id: personaData.project_id,
      persona_name: personaData.persona_name,
      age: personaData.age,
      gender: personaData.gender,
      location: personaData.location,
      education: personaData.education,
      income_level: personaData.income_level,
      marital_status: personaData.marital_status,
      job_info: personaData.job_info,
      personal_background: personaData.personal_background,
      goals: personaData.goals || [],
      challenges: personaData.challenges || [],
      objections: personaData.objections || [],
      communication_channels: personaData.communication_channels || [],
      influences: personaData.influences || [],
      quotes: personaData.quotes || [],
      typical_day: personaData.typical_day,
      brand_affinities: personaData.brand_affinities || [],
      buying_process: personaData.buying_process,
      avatar_description: personaData.avatar_description,
      avatar_url: personaData.avatar_url,
      avatar_id: personaData.avatar_id,
      generation_prompt: personaData.generation_prompt,
      ai_model_used: personaData.ai_model_used,
      generation_timestamp: personaData.generation_timestamp,
      is_favorite: false,
      custom_name: personaData.custom_name,
      notes: personaData.notes,
      conversation_count: 0,
      view_count: 0
    }

    const { data, error } = await supabase
      .from('buyer_personas')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      console.error('Error creating buyer persona:', error)
      throw new Error(`Failed to create persona: ${error.message}`)
    }

    // Update project personas count
    await this.updateProjectPersonasCount(personaData.project_id)

    return data
  }

  /**
   * Update a buyer persona
   */
  async updatePersona(updateData: UpdateBuyerPersonaData): Promise<BuyerPersona> {
    const user = await this.getCurrentUser()
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .from('buyer_personas')
      .update(updates)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating buyer persona:', error)
      throw new Error(`Failed to update persona: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a buyer persona
   */
  async deletePersona(id: string): Promise<void> {
    const user = await this.getCurrentUser()

    // Get persona to update project count
    const persona = await this.getPersonaById(id)
    if (!persona) {
      throw new Error('Persona not found')
    }

    const { error } = await supabase
      .from('buyer_personas')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting buyer persona:', error)
      throw new Error(`Failed to delete persona: ${error.message}`)
    }

    // Update project personas count
    await this.updateProjectPersonasCount(persona.project_id)
  }

  /**
   * Toggle favorite status for a persona
   */
  async togglePersonaFavorite(id: string, isFavorite: boolean): Promise<BuyerPersona> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_personas')
      .update({ is_favorite: isFavorite })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling persona favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  /**
   * Update persona view count
   */
  async updatePersonaView(id: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { error } = await supabase
      .from('buyer_personas')
      .update({ view_count: supabase.sql`view_count + 1` })
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error updating persona view:', error)
      // Don't throw error for view tracking
    }
  }

  // ===============================================
  // CONVERSATION OPERATIONS
  // ===============================================

  /**
   * Get all conversations for a persona
   */
  async getConversationsByPersona(personaId: string): Promise<BuyerPersonaConversation[]> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .select('*')
      .eq('persona_id', personaId)
      .eq('user_id', user.id)
      .order('last_message_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching conversations:', error)
      throw new Error(`Failed to fetch conversations: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get all conversations for a project
   */
  async getConversationsByProject(projectId: string): Promise<BuyerPersonaConversation[]> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .select('*')
      .eq('project_id', projectId)
      .eq('user_id', user.id)
      .order('last_message_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching project conversations:', error)
      throw new Error(`Failed to fetch conversations: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get a specific conversation by ID
   */
  async getConversationById(id: string): Promise<BuyerPersonaConversation | null> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      throw new Error(`Failed to fetch conversation: ${error.message}`)
    }

    return data
  }

  /**
   * Create a new conversation
   */
  async createConversation(conversationData: CreateBuyerPersonaConversationData): Promise<BuyerPersonaConversation> {
    const user = await this.getCurrentUser()

    const insertData = {
      user_id: user.id,
      project_id: conversationData.project_id,
      persona_id: conversationData.persona_id,
      conversation_title: conversationData.conversation_title,
      conversation_context: conversationData.conversation_context,
      messages: conversationData.messages || [],
      total_messages: conversationData.messages?.length || 0,
      conversation_status: 'active' as const,
      key_insights: [],
      is_favorite: false,
      tags: conversationData.tags || [],
      notes: conversationData.notes,
      view_count: 0,
      last_message_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .insert(insertData)
      .select()
      .single()

    if (error) {
      console.error('Error creating conversation:', error)
      throw new Error(`Failed to create conversation: ${error.message}`)
    }

    // Update persona and project conversation counts
    await this.updatePersonaConversationCount(conversationData.persona_id)
    await this.updateProjectConversationsCount(conversationData.project_id)

    return data
  }

  /**
   * Update a conversation
   */
  async updateConversation(updateData: UpdateBuyerPersonaConversationData): Promise<BuyerPersonaConversation> {
    const user = await this.getCurrentUser()
    const { id, ...updates } = updateData

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .update(updates)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating conversation:', error)
      throw new Error(`Failed to update conversation: ${error.message}`)
    }

    return data
  }

  /**
   * Add a message to a conversation
   */
  async addMessageToConversation(
    conversationId: string,
    message: { role: 'user' | 'assistant', content: string, timestamp: string }
  ): Promise<BuyerPersonaConversation> {
    const user = await this.getCurrentUser()

    // Get current conversation
    const conversation = await this.getConversationById(conversationId)
    if (!conversation) {
      throw new Error('Conversation not found')
    }

    const updatedMessages = [...conversation.messages, message]

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .update({
        messages: updatedMessages,
        total_messages: updatedMessages.length,
        last_message_at: message.timestamp
      })
      .eq('id', conversationId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error adding message to conversation:', error)
      throw new Error(`Failed to add message: ${error.message}`)
    }

    return data
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(id: string): Promise<void> {
    const user = await this.getCurrentUser()

    // Get conversation to update counts
    const conversation = await this.getConversationById(id)
    if (!conversation) {
      throw new Error('Conversation not found')
    }

    const { error } = await supabase
      .from('buyer_persona_conversations')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting conversation:', error)
      throw new Error(`Failed to delete conversation: ${error.message}`)
    }

    // Update persona and project conversation counts
    await this.updatePersonaConversationCount(conversation.persona_id)
    await this.updateProjectConversationsCount(conversation.project_id)
  }

  /**
   * Toggle favorite status for a conversation
   */
  async toggleConversationFavorite(id: string, isFavorite: boolean): Promise<BuyerPersonaConversation> {
    const user = await this.getCurrentUser()

    const { data, error } = await supabase
      .from('buyer_persona_conversations')
      .update({ is_favorite: isFavorite })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error toggling conversation favorite:', error)
      throw new Error(`Failed to toggle favorite: ${error.message}`)
    }

    return data
  }

  // ===============================================
  // HELPER METHODS
  // ===============================================

  /**
   * Update project personas count
   */
  private async updateProjectPersonasCount(projectId: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { count, error: countError } = await supabase
      .from('buyer_personas')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting personas:', countError)
      return
    }

    const { error } = await supabase
      .from('buyer_persona_projects')
      .update({ personas_count: count || 0 })
      .eq('id', projectId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error updating project personas count:', error)
    }
  }

  /**
   * Update project conversations count
   */
  private async updateProjectConversationsCount(projectId: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { count, error: countError } = await supabase
      .from('buyer_persona_conversations')
      .select('*', { count: 'exact', head: true })
      .eq('project_id', projectId)
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting conversations:', countError)
      return
    }

    const { error } = await supabase
      .from('buyer_persona_projects')
      .update({ conversations_count: count || 0 })
      .eq('id', projectId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error updating project conversations count:', error)
    }
  }

  /**
   * Update persona conversation count
   */
  private async updatePersonaConversationCount(personaId: string): Promise<void> {
    const user = await this.getCurrentUser()

    const { count, error: countError } = await supabase
      .from('buyer_persona_conversations')
      .select('*', { count: 'exact', head: true })
      .eq('persona_id', personaId)
      .eq('user_id', user.id)

    if (countError) {
      console.error('Error counting persona conversations:', countError)
      return
    }

    const { error } = await supabase
      .from('buyer_personas')
      .update({
        conversation_count: count || 0,
        last_conversation_at: new Date().toISOString()
      })
      .eq('id', personaId)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error updating persona conversation count:', error)
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    totalProjects: number
    totalPersonas: number
    totalConversations: number
    favoriteProjects: number
    favoritePersonas: number
    favoriteConversations: number
  }> {
    const user = await this.getCurrentUser()

    const [
      { count: totalProjects },
      { count: totalPersonas },
      { count: totalConversations },
      { count: favoriteProjects },
      { count: favoritePersonas },
      { count: favoriteConversations }
    ] = await Promise.all([
      supabase.from('buyer_persona_projects').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
      supabase.from('buyer_personas').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
      supabase.from('buyer_persona_conversations').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
      supabase.from('buyer_persona_projects').select('*', { count: 'exact', head: true }).eq('user_id', user.id).eq('is_favorite', true),
      supabase.from('buyer_personas').select('*', { count: 'exact', head: true }).eq('user_id', user.id).eq('is_favorite', true),
      supabase.from('buyer_persona_conversations').select('*', { count: 'exact', head: true }).eq('user_id', user.id).eq('is_favorite', true)
    ])

    return {
      totalProjects: totalProjects || 0,
      totalPersonas: totalPersonas || 0,
      totalConversations: totalConversations || 0,
      favoriteProjects: favoriteProjects || 0,
      favoritePersonas: favoritePersonas || 0,
      favoriteConversations: favoriteConversations || 0
    }
  }
}

// Export singleton instance
export const buyerPersonaService = new BuyerPersonaService()
