import { createClient } from '@supabase/supabase-js'

// Configuración de Supabase - Valores fijos para evitar problemas de env
const supabaseUrl = 'https://pthewpjbegkgomvyhkin.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB0aGV3cGpiZWdrZ29tdnloa2luIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MjM1NDMsImV4cCI6MjA2NDI5OTU0M30.bskxkyZ9meYb2cpZZGmS_FAS2Wyjs4j_lOPnJqh1s0k'

console.log('🔧 Supabase configurado:', {
  url: supabaseUrl,
  keyPrefix: supabaseAnonKey.substring(0, 20) + '...'
})

// Create Supabase client with enhanced configuration for better error handling
// Default client uses 'public' schema for most tables (marcas, moodboards, etc.)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'  // Default to 'public' schema for most tables
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  }
})

// Create separate client for 'api' schema tables (design_analyses, user_palettes, etc.)
// IMPORTANT: This client shares the same auth instance to avoid multiple GoTrueClient warnings
export const supabaseApi = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: false,  // Disable to prevent duplicate auth instances
    persistSession: false,   // Use main client for session management
    detectSessionInUrl: false // Use main client for URL detection
  },
  db: {
    schema: 'api'  // Use 'api' schema for specific tables
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  }
})

// Helper function to get authenticated supabaseApi client
export const getAuthenticatedApiClient = () => {
  // Copy the session from the main client to the API client
  const session = supabase.auth.getSession();
  if (session) {
    supabaseApi.auth.setSession(session);
  }
  return supabaseApi;
}

// Add global error handler for Supabase client (only on main client)
supabase.auth.onAuthStateChange((event, session) => {
  console.log('🔐 Supabase auth state change:', { event, userId: session?.user?.id, email: session?.user?.email });

  // Sync session to API client when auth state changes
  if (session) {
    supabaseApi.auth.setSession(session);
  }
})

// Tipos para Marca
export interface Marca {
  id: string
  created_at: string
  updated_at: string
  
  // Información básica
  brand_name: string
  website?: string
  industry: string
  
  // Identidad visual
  logo_url?: string
  primary_color: string
  secondary_color: string
  
  // Audiencia y tono
  target_audience: string
  tone: string
  personality: string[]
  
  // Posicionamiento
  description: string
  unique_value: string
  competitors?: string
  
  // Documentos y ejemplos
  documents?: string[]
  examples?: string
  
  // Metadata
  status: 'draft' | 'active' | 'archived'
  campaigns_count: number
  assets_count: number
  
  // Usuario
  user_id?: string
}

export interface CreateMarcaData {
  brand_name: string
  website?: string
  industry: string
  logo_url?: string
  primary_color: string
  secondary_color: string
  target_audience: string
  tone: string
  personality: string[]
  description: string
  unique_value: string
  competitors?: string
  documents?: string[]
  examples?: string
  user_id?: string
}

export interface UpdateMarcaData extends Partial<CreateMarcaData> {
  id: string
}

// ===============================================
// DESIGN TOOLS TYPES
// ===============================================

// Tipos para simulaciones de focus group
export interface FocusGroupSimulation {
  id: string
  created_at: string
  updated_at: string

  // Usuario propietario
  user_id: string

  // Parámetros de entrada
  content: string
  product_category?: string
  context?: string
  custom_questions: string[]
  num_participants: number
  discussion_rounds: number

  // Resultados de la simulación
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any

  // Metadata
  simulation_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string

  // Gestión de favoritos y organización
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string

  // Estadísticas
  last_viewed_at?: string
  regeneration_count: number
}

export interface CreateFocusGroupSimulationData {
  content: string
  product_category?: string
  context?: string
  custom_questions?: string[]
  num_participants?: number
  discussion_rounds?: number
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any
  simulation_duration_ms?: number
  custom_name?: string
  tags?: string[]
  notes?: string
}

export interface UpdateFocusGroupSimulationData {
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  last_viewed_at?: string
  regeneration_count?: number
}

// Tipos para análisis de complejidad visual
export interface DesignAnalysis {
  id: string
  created_at: string
  updated_at: string

  // Usuario propietario
  user_id: string

  // Información del archivo analizado
  original_filename: string
  file_size?: number
  file_type?: string
  file_url?: string // URL del archivo en Supabase Storage

  // Parámetros de análisis
  tool_type: string
  analysis_version?: string

  // Resultados del análisis
  overall_score: number
  complexity_scores: {
    color?: number
    layout?: number
    typography?: number
    elements?: number
    hierarchy?: number
    composition?: number
    contrast?: number
    whitespace?: number
  }
  analysis_areas: Array<{
    name: string
    score: number
    description: string
    recommendations?: string[]
  }>
  recommendations: Array<{
    category: string
    issue: string
    importance: 'alta' | 'media' | 'baja'
    recommendation: string
  }>

  // Análisis detallado de IA
  ai_analysis_summary?: string
  gemini_analysis?: string
  agent_message?: string
  visuai_insights?: any

  // Metadata adicional
  analysis_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string

  // Gestión de favoritos y organización
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string

  // Estadísticas
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

// Tipos para moodboards interactivos
export interface Moodboard {
  id: string
  created_at: string
  updated_at: string

  // Usuario propietario
  user_id: string

  // Información básica del moodboard
  title: string
  description?: string

  // Configuración del moodboard
  tool_type: string
  version?: string

  // Datos del canvas de Tldraw
  tldraw_data?: any // Datos completos del estado de Tldraw
  canvas_snapshot?: string // URL de imagen snapshot del canvas

  // Metadatos del proyecto
  tags: string[]
  is_public: boolean
  is_favorite: boolean

  // Configuración de colaboración
  collaboration_enabled: boolean
  shared_with: string[] // Array de user_ids con acceso

  // Estadísticas de uso
  view_count: number
  last_viewed_at?: string

  // Estado del proyecto
  status: string

  // Notas adicionales
  notes?: string
}

export interface MoodboardHistory {
  id: string
  created_at: string

  // Referencia al moodboard
  moodboard_id: string
  user_id: string

  // Datos del cambio
  change_type: 'create' | 'update' | 'snapshot'
  change_description?: string
  tldraw_data_snapshot?: any

  // Metadatos del cambio
  version_number: number
  is_auto_save: boolean
}

export interface CreateDesignAnalysisData {
  user_id: string
  original_filename: string
  file_size?: number
  file_type?: string
  file_url?: string
  tool_type?: string
  analysis_version?: string
  overall_score: number
  complexity_scores: DesignAnalysis['complexity_scores']
  analysis_areas: DesignAnalysis['analysis_areas']
  recommendations: DesignAnalysis['recommendations']
  ai_analysis_summary?: string
  gemini_analysis?: string
  agent_message?: string
  visuai_insights?: any
  analysis_duration_ms?: number
  status?: 'processing' | 'completed' | 'failed'
  error_message?: string
  is_favorite?: boolean
  custom_name?: string
  tags?: string[]
  notes?: string
}

export interface UpdateDesignAnalysisData {
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

// Tipos para archivos subidos
export interface DesignUpload {
  id: string
  created_at: string

  // Usuario propietario
  user_id: string

  // Información del archivo
  original_filename: string
  file_size: number
  file_type: string
  file_url: string
  file_hash?: string

  // Metadata
  upload_source: string
  is_processed: boolean

  // Relación con análisis
  analysis_id?: string
}

export interface CreateDesignUploadData {
  user_id: string
  original_filename: string
  file_size: number
  file_type: string
  file_url: string
  file_hash?: string
  upload_source?: string
  is_processed?: boolean
  analysis_id?: string
}

// Tipos para paletas de colores de usuario
export interface UserPalette {
  id: string
  created_at: string
  updated_at: string

  // Usuario propietario
  user_id: string

  // Información de la paleta
  name: string
  colors: string[] // Array de códigos de color hex: ["#FF5733", "#33FF57", "#3357FF"]

  // Metadata adicional
  description?: string
  tags: string[]
  is_favorite: boolean
}

export interface CreateUserPaletteData {
  name: string
  colors: string[]
  description?: string
  tags?: string[]
  is_favorite?: boolean
}

export interface UpdateUserPaletteData {
  name?: string
  colors?: string[]
  description?: string
  tags?: string[]
  is_favorite?: boolean
}

// Tipos para análisis de títulos/headlines
export interface HeadlineAnalysis {
  id: string
  created_at: string
  updated_at: string

  // Usuario propietario
  user_id: string

  // Información del título analizado
  headline_text: string
  content_type: string
  audience_context?: string

  // Parámetros de análisis
  tool_type: string
  analysis_version: string

  // Resultados del análisis
  overall_score: number
  basic_analysis: any // JSONB
  advanced_analysis: any // JSONB
  recommendations: any // JSONB

  // Metadata adicional
  analysis_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string

  // Gestión de favoritos y organización
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string

  // Estadísticas de uso
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

export interface CreateHeadlineAnalysisData {
  user_id: string
  headline_text: string
  content_type: string
  audience_context?: string
  tool_type?: string
  analysis_version?: string
  overall_score: number
  basic_analysis: any
  advanced_analysis: any
  recommendations: any
  analysis_duration_ms?: number
  status?: 'processing' | 'completed' | 'failed'
  error_message?: string
  is_favorite?: boolean
  custom_name?: string
  tags?: string[]
  notes?: string
}

export interface UpdateHeadlineAnalysisData {
  id: string
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

// Respuestas de la API para paletas
export interface PaletteApiResponse {
  success: boolean
  message?: string
  palette?: UserPalette
}

export interface PalettesListApiResponse {
  success: boolean
  palettes: UserPalette[]
  count: number
  user_id: string
}
